module.exports = {
  //主题名称
  themeNameZh: "主题-01",
  themeNameEn: "Theme-01",

  //是否显示 tagsView
  tagsView: true,

  //是否显示logo
  sidebarLogo: true,

  //是否显示动态标题
  dynamicTitle: false,

  // 主题色
  theme: "red",

  //侧边栏类名为theme-custom时,下面关于菜单的设置才会生效
  sideTheme: "theme-custom",

  // 菜单文字颜色
  menuColorCustom: "#000",

  // 背景颜色-主菜单
  menuBackgroundCustom: "#fff",

  // 菜单文字颜色-高亮
  menuColorActiveCustom: "#1C6CDD",

  // 背景颜色-子菜单
  subMenuBackgroundCustom: "#fff",

  // 背景颜色-菜单高亮
  subMenuBackgroundActiveCustom: "#e6f3ff",

  // 背景颜色-鼠标悬停阴影色
  subMenuHoverCustom: "#1C6CDD19",

  // 顶部导航栏背景色
  topBackgroundCustom: "#fff",

  // 顶部导航栏图标颜色
  topSvgCustom: "#231815",
};
